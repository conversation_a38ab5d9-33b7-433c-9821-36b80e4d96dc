import { theme } from "ant-design-vue";
import type { ThemeConfig } from "ant-design-vue/es/config-provider/context";
import { ref, computed, onMounted, onUnmounted, type Ref } from "vue";

// 主题类型定义
export type ThemeType = "default" | "dark" | "compact" | "custom";

// 存储主题信息
const storageKey = "app-theme";

// 存储主题信息类型
interface StorageThemeInfo {
  // 主题类型
  themeType: ThemeType;
}

// 应用主题配置
interface ApplyThemeConfig {
  themeType: ThemeType;
  token?: string | typeof defaultTokens.colorPrimary;
}

// 默认的主题算法映射
const themeAlgorithms = {
  default: theme.defaultAlgorithm,
  dark: theme.darkAlgorithm,
  compact: theme.compactAlgorithm,
  custom: theme.defaultAlgorithm, // 自定义主题基于默认算法，通过 token 定制
};

// 默认主题 token
export const defaultTokens = {
  colorPrimary: "#1f74ec",
};

// 自定义主题 token
export const customTokens = {
  colorPrimary: "#6366f1",
  colorSuccess: "#52c41a",
  colorWarning: "#faad14",
  colorError: "#ff4d4f",
  colorInfo: "#1890ff",
};

// 主题配置生成器
export function getThemeConfig(themeType: ThemeType, customColor?: string): ThemeConfig {
  const colorPrimary = themeType === "custom" && customColor
    ? customColor
    : themeType === "custom"
    ? customTokens.colorPrimary
    : defaultTokens.colorPrimary;

  console.log("getThemeConfig 调用:", {
    themeType,
    customColor,
    resultColorPrimary: colorPrimary
  });

  const baseConfig: ThemeConfig = {
    token: {
      colorPrimary,
    },
    algorithm: themeAlgorithms[themeType],
  };
  return baseConfig;
}

// 主题状态管理
class ThemeManager {
  // 当前主题类型
  private currentTheme: ThemeType = "dark";
  // 当前自定义主题色
  private customThemeColor: string = defaultTokens.colorPrimary;
  private listeners: Array<(theme: ThemeType) => void> = [];

  constructor() {
    // 从 localStorage 恢复主题设置
    const storageThemeInfo = JSON.parse(
      localStorage.getItem(storageKey) || "{}"
    ) as any;
    if (storageThemeInfo && this.isValidTheme(storageThemeInfo.themeType)) {
      this.currentTheme = storageThemeInfo.themeType;
      // 恢复自定义主题色
      if (storageThemeInfo.token) {
        this.customThemeColor = storageThemeInfo.token;
      }
    } else {
      localStorage.setItem(
        storageKey,
        JSON.stringify({
          themeType: this.currentTheme,
          token: this.customThemeColor,
        })
      );
    }
  }

  // 验证主题类型是否有效
  private isValidTheme(theme: string): theme is ThemeType {
    return ["default", "dark", "compact", "custom"].includes(theme);
  }

  // 获取当前主题类型
  getCurrentTheme(): ThemeType {
    return this.currentTheme;
  }

  // 获取当前自定义主题色
  getCurrentCustomColor(): string {
    return this.customThemeColor;
  }

  // 获取当前主题配置
  getCurrentThemeConfig(): ThemeConfig {
    return getThemeConfig(this.currentTheme, this.customThemeColor);
  }

  // 设置主题类型
  setTheme(applyThemeConfig: ApplyThemeConfig): void {
    this.currentTheme = applyThemeConfig.themeType;

    // 如果有自定义颜色，保存它
    if (applyThemeConfig.token) {
      this.customThemeColor = applyThemeConfig.token as string;
    }

    // 将主题类型和自定义颜色存储到localStorage
    localStorage.setItem(
      storageKey,
      JSON.stringify({
        themeType: applyThemeConfig.themeType,
        token: this.customThemeColor,
      })
    );

    console.log("主题已设置:", {
      themeType: this.currentTheme,
      customColor: this.customThemeColor
    });

    // 通知主题变化
    this.notifyListeners();
  }

  // 订阅主题变化
  subscribe(listener: (theme: ThemeType) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  // 通知主题变化
  private notifyListeners(): void {
    this.listeners.forEach((listener) => listener(this.currentTheme));
  }
}

// 导出单例实例
export const themeManager = new ThemeManager();

// 主题信息配置
export const themeInfo = {
  default: {
    name: "默认主题",
    description: "标准的 Ant Design 主题",
    preview: "linear-gradient(45deg, #1890ff, #52c41a)",
  },
  dark: {
    name: "暗色主题",
    description: "适合夜间使用的深色主题",
    preview: "linear-gradient(45deg, #434343, #262626)",
  },
  compact: {
    name: "紧凑主题",
    description: "更紧凑的布局和间距",
    preview: "linear-gradient(45deg, #1890ff, #722ed1)",
  },
  custom: {
    name: "自定义主题",
    description: "自定义品牌主题",
    preview: "linear-gradient(45deg, #6366f1, #8b5cf6)",
  },
} as const;

// 工具函数：获取主题信息
export function getThemeSettingInfo(themeType: ThemeType) {
  return themeInfo[themeType];
}

// 工具函数：获取所有可用主题
export function getAllThemes(): Array<{
  key: ThemeType;
  info: (typeof themeInfo)[ThemeType];
}> {
  return Object.entries(themeInfo).map(([key, info]) => ({
    key: key as ThemeType,
    info,
  }));
}

// 工具函数：切换主题
export function applyTheme(applyThemeConfig: ApplyThemeConfig): void {
  themeManager.setTheme(applyThemeConfig);
}

// 工具函数：获取当前主题
export function getCurrentTheme(): ThemeType {
  return themeManager.getCurrentTheme();
}

// 工具函数：获取当前自定义颜色
export function getCurrentCustomColor(): string {
  return themeManager.getCurrentCustomColor();
}

// 工具函数：获取当前主题配置
export function getCurrentThemeConfig(): ThemeConfig {
  return themeManager.getCurrentThemeConfig();
}

// 扩展接口：创建自定义主题配置
export function createCustomThemeConfig(
  baseTheme: ThemeType = "default"
): ThemeConfig {
  const baseConfig = getThemeConfig(baseTheme);

  // 合并自定义 token
  const mergedTokens = {
    ...baseConfig.token,
    ...customTokens,
  };

  return {
    ...baseConfig,
    token: mergedTokens,
  };
}

// Vue 组合式函数：响应式主题管理
export function useTheme() {
  const currentTheme: Ref<ThemeType> = ref(themeManager.getCurrentTheme());
  const currentCustomColor: Ref<string> = ref(themeManager.getCurrentCustomColor());
  const currentThemeConfig = computed(() =>
    getThemeConfig(currentTheme.value, currentCustomColor.value)
  );

  // 在组件内部使用 theme.useToken() 获取响应式的主题token
  const { token } = theme.useToken();

  // 响应式地根据当前主题token计算颜色信息
  const currentThemeSettingInfo = computed(() =>
    getThemeSettingInfo(currentTheme.value)
  );

  let unsubscribe: (() => void) | null = null;

  onMounted(() => {
    // 订阅主题变化
    unsubscribe = themeManager.subscribe((newTheme: ThemeType) => {
      currentTheme.value = newTheme;
      currentCustomColor.value = themeManager.getCurrentCustomColor();
    });
  });

  onUnmounted(() => {
    // 清理订阅
    if (unsubscribe) {
      unsubscribe();
    }
  });

  return {
    currentTheme: computed(() => currentTheme.value),
    currentThemeConfig,
    currentThemeSettingInfo,
    token: computed(() => token.value),
    getAllThemes,
  };
}
