<template>
  <div class="theme-switcher">
    <!-- 主题切换按钮 -->
    <a-button type="text" class="theme-btn" @click="showDrawer">
      <template #icon>
        <BgColorsOutlined />
      </template>
      主题
    </a-button>

    <!-- 主题配置抽屉 -->
    <a-drawer
      title="主题配置"
      placement="right"
      :open="drawerVisible"
      @close="closeDrawer"
      width="360"
    >
      <div class="theme-config-content">
        <!-- 当前主题信息卡片 -->
        <a-card class="current-theme-card" :bordered="false">
          <template #title>
            <small class="text-secondary">当前主题</small>
          </template>
          <div class="current-theme-info">
            <div class="theme-name-display">{{ currentThemeSettingInfo.name }}</div>
            <div class="theme-colors-demo">
              <span class="color-demo bg-primary" title="主题色"></span>
              <span class="color-demo bg-success" title="成功色"></span>
              <span class="color-demo bg-warning" title="警告色"></span>
              <span class="color-demo bg-error" title="错误色"></span>
            </div>
          </div>
        </a-card>

        <!-- 主题选择区域 -->
        <div class="config-section">
          <h3 class="section-title">选择主题</h3>
          <a-row :gutter="[12, 12]">
            <a-col
              :span="12"
              v-for="theme in availableThemes"
              :key="theme.key"
            >
              <a-card
                size="small"
                :class="['theme-option-card', { 'selected': currentTheme === theme.key }]"
                @click="handleThemeChange({ key: theme.key })"
                hoverable
              >
                <div class="theme-option">
                  <div
                    class="theme-preview"
                    :style="{ background: theme.info.preview }"
                  ></div>
                  <div class="theme-info">
                    <div class="theme-name">{{ theme.info.name }}</div>
                    <div class="theme-description">{{ theme.info.description }}</div>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 自定义主题色配置 -->
        <div class="config-section">
          <h3 class="section-title">自定义主题色</h3>
          <a-space direction="vertical" style="width: 100%;" size="middle">
            <div class="color-picker-section">
              <a-input
                v-model:value="customColorPrimary"
                placeholder="#1f74ec"
                @change="handleColorChange"
              >
                <template #addonBefore>
                  <div
                    class="color-preview-small"
                    :style="{ backgroundColor: customColorPrimary }"
                  ></div>
                </template>
                <template #addonAfter>
                  <div class="color-input-addon">
                    <input
                      type="color"
                      :value="customColorPrimary"
                      @input="handleColorPickerChange"
                    />
                  </div>
                </template>
              </a-input>
            </div>

            <a-row :gutter="12">
              <a-col :span="12">
                <a-button
                  type="primary"
                  block
                  @click="applyCustomTheme"
                  :disabled="!isCustomColorChanged"
                >
                  应用自定义主题
                </a-button>
              </a-col>
              <a-col :span="12">
                <a-button
                  block
                  @click="resetToDefaultColor"
                  :disabled="!isCustomColorChanged"
                >
                  重置默认色
                </a-button>
              </a-col>
            </a-row>
          </a-space>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { BgColorsOutlined } from '@ant-design/icons-vue'
import {
  applyTheme,
  getAllThemes,
  getThemeSettingInfo,
  useTheme,
  defaultTokens,
  getCurrentTheme,
  type ThemeType
} from '@/theme/config'
import { message } from 'ant-design-vue'

// 使用 useTheme 组合式函数获取响应式主题配置
const {
  currentTheme,
  currentThemeSettingInfo
} = useTheme()

// 抽屉可见性状态
const drawerVisible = ref(false)

// 可用主题列表
const availableThemes = getAllThemes()

// 自定义主题色
const customColorPrimary = ref(defaultTokens.colorPrimary)

const themeType = ref<ThemeType>(getCurrentTheme())

// 计算自定义颜色是否已更改
const isCustomColorChanged = computed(() => {
  return customColorPrimary.value !== defaultTokens.colorPrimary
})

// 显示抽屉
const showDrawer = () => {
  drawerVisible.value = true
}

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false
}

const handleThemeChange = ({ key }: { key: string }) => {
  themeType.value = key as ThemeType
  const themeInfo = getThemeSettingInfo(themeType.value)

  applyTheme({themeType: themeType.value})
  message.success(`已切换到${themeInfo.name}`)
}

// 处理颜色输入变化
const handleColorChange = (e: any) => {
  const colorValue = e.target.value
  // 验证颜色格式
  if (/^#[0-9A-Fa-f]{6}$/.test(colorValue)) {
    customColorPrimary.value = colorValue
  }
}

// 处理颜色选择器变化
const handleColorPickerChange = (e: any) => {
  customColorPrimary.value = e.target.value
}

// 应用自定义主题
const applyCustomTheme = () => {
  // 创建并应用自定义主题
  console.log("themeType:",  themeType.value)
  applyTheme({ themeType: themeType.value, token: customColorPrimary.value })
  message.success("自定义主题已应用")
}

// 重置为默认主题色
const resetToDefaultColor = () => {
  customColorPrimary.value = defaultTokens.colorPrimary
  applyTheme({ themeType: "custom", token: defaultTokens.colorPrimary })
  message.success("已重置为默认主题色")
}

onMounted(() => {
  // 如果当前是自定义主题，获取其主题色
  if (currentTheme.value === "custom") {
    const savedCustomColor = localStorage.getItem("custom-theme-primary-color")
    if (savedCustomColor) {
      customColorPrimary.value = savedCustomColor
    }
  }
})
</script>

<style scoped>
.theme-switcher {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.theme-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--theme-bg-elevated);
  border: 1px solid var(--theme-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  transition: all 0.2s;
}

.theme-btn:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.theme-config-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.current-theme-card {
  margin-bottom: 16px;
}

.current-theme-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.theme-name-display {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-base);
}

.theme-colors-demo {
  display: flex;
  gap: 8px;
}

.color-demo {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: inline-block;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: 1px solid var(--theme-border);
}

.color-demo:hover {
  transform: scale(1.1);
}

.bg-primary {
  background-color: var(--theme-primary);
}

.bg-success {
  background-color: var(--theme-success);
}

.bg-warning {
  background-color: var(--theme-warning);
}

.bg-error {
  background-color: var(--theme-error);
}

.config-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--theme-text-base);
}

.theme-option-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.theme-option-card:hover {
  border-color: var(--theme-primary);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.theme-option-card.selected {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary-bg);
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 12px;
}

.theme-preview {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: 1px solid var(--theme-border);
  flex-shrink: 0;
}

.theme-info {
  flex: 1;
  min-width: 0;
}

.theme-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  color: var(--theme-text-base);
  margin-bottom: 2px;
}

.theme-description {
  font-size: 12px;
  color: var(--theme-text-secondary);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.color-picker-section {
  width: 100%;
}

.color-preview-small {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid var(--theme-border);
}

.color-input-addon {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 4px;
}

.color-input-addon input[type="color"] {
  width: 24px;
  height: 24px;
  border: none;
  padding: 0;
  background: none;
  cursor: pointer;
  border-radius: 4px;
}

.text-secondary {
  color: var(--theme-text-secondary);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 暗色主题下的样式调整 */
:global(.ant-theme-dark) .theme-btn {
  background: rgba(20, 20, 20, 0.9);
  border-color: var(--theme-border);
  color: var(--theme-text-base);
}

:global(.ant-theme-dark) .theme-name {
  color: var(--theme-text-base);
}

:global(.ant-theme-dark) .theme-description {
  color: var(--theme-text-secondary);
}

:global(.ant-theme-dark) .theme-option-card.selected {
  background-color: rgba(24, 144, 255, 0.1);
}
</style>
